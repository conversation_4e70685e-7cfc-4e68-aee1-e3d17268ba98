// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter_test/flutter_test.dart';

import 'package:my_school/main.dart';

void main() {
  testWidgets('Splash screen loads correctly', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const MySchoolApp());

    // Verify that splash screen elements are present.
    expect(find.text('CREATIVE READERS'), findsOneWidget);
    expect(find.text('PUBLICATION'), findsOneWidget);
    expect(find.text('Choose your option'), findsOneWidget);
    expect(find.text('Student'), findsOneWidget);
    expect(find.text('Teacher'), findsOneWidget);
    expect(find.text('Guest'), findsOneWidget);
  });
}
