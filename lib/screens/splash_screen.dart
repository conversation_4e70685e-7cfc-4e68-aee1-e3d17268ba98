import 'package:flutter/material.dart';
import '../theme/app_theme.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.0, 0.6, curve: Curves.easeOut),
      ),
    );

    _scaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.2, 0.8, curve: Curves.elasticOut),
      ),
    );

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: AppTheme.splashGradientDecoration,
        child: SafeArea(
          child: AnimatedBuilder(
            animation: _animationController,
            builder: (context, child) {
              return Opacity(
                opacity: _fadeAnimation.value,
                child: Transform.scale(
                  scale: _scaleAnimation.value,
                  child: _buildContent(context),
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildContent(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.height < 600;
    final logoSize = isSmallScreen ? 100.0 : 120.0;
    final fontSize = isSmallScreen ? 16.0 : 18.0;
    final buttonSize = isSmallScreen ? 70.0 : 80.0;

    return Column(
      children: [
        // Top curved section with logo
        Expanded(
          flex: isSmallScreen ? 2 : 3,
          child: Container(
            width: double.infinity,
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(100),
                bottomRight: Radius.circular(100),
              ),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Logo container with pulse animation
                TweenAnimationBuilder<double>(
                  duration: const Duration(seconds: 2),
                  tween: Tween(begin: 0.8, end: 1.0),
                  builder: (context, value, child) {
                    return Transform.scale(
                      scale: value,
                      child: Container(
                        width: logoSize,
                        height: logoSize,
                        decoration: AppTheme.logoContainerDecoration,
                        child: Icon(
                          Icons.school,
                          size: logoSize * 0.5,
                          color: AppTheme.primaryBlue,
                        ),
                      ),
                    );
                  },
                ),
                SizedBox(height: isSmallScreen ? 15 : 20),
                // App name with slide animation
                TweenAnimationBuilder<Offset>(
                  duration: const Duration(milliseconds: 800),
                  tween: Tween(begin: const Offset(0, 50), end: Offset.zero),
                  builder: (context, offset, child) {
                    return Transform.translate(
                      offset: offset,
                      child: Column(
                        children: [
                          Text(
                            'CREATIVE READERS',
                            style: AppTheme.splashTitle.copyWith(
                              fontSize: fontSize,
                            ),
                          ),
                          Text(
                            'PUBLICATION',
                            style: AppTheme.splashSubtitle.copyWith(
                              fontSize: fontSize - 4,
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
        // Bottom section with user options
        Expanded(
          flex: isSmallScreen ? 3 : 2,
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: screenSize.width * 0.1),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  'Choose your option',
                  style: AppTheme.splashOption.copyWith(fontSize: fontSize),
                ),
                SizedBox(height: isSmallScreen ? 30 : 40),
                // User role buttons with staggered animation
                TweenAnimationBuilder<double>(
                  duration: const Duration(milliseconds: 1200),
                  tween: Tween(begin: 0.0, end: 1.0),
                  builder: (context, value, child) {
                    return Opacity(
                      opacity: value,
                      child: Transform.translate(
                        offset: Offset(0, 30 * (1 - value)),
                        child: Column(
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                              children: [
                                _buildRoleButton(
                                  context,
                                  icon: Icons.school,
                                  label: 'Student',
                                  size: buttonSize,
                                  onTap: () => _navigateToRole('student'),
                                ),
                                _buildRoleButton(
                                  context,
                                  icon: Icons.person_outline,
                                  label: 'Teacher',
                                  size: buttonSize,
                                  onTap: () => _navigateToRole('teacher'),
                                ),
                              ],
                            ),
                            SizedBox(height: isSmallScreen ? 15 : 20),
                            _buildRoleButton(
                              context,
                              icon: Icons.person,
                              label: 'Guest',
                              size: buttonSize,
                              onTap: () => _navigateToRole('guest'),
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
                const SizedBox(height: 20),
                // Loading indicator
                TweenAnimationBuilder<double>(
                  duration: const Duration(milliseconds: 1500),
                  tween: Tween(begin: 0.0, end: 1.0),
                  builder: (context, value, child) {
                    return Opacity(
                      opacity: value,
                      child: const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Colors.white,
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildRoleButton(
    BuildContext context, {
    required IconData icon,
    required String label,
    required double size,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: size,
        height: size,
        decoration: AppTheme.roleButtonDecoration(size),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: size * 0.4, color: Colors.white),
            SizedBox(height: size * 0.1),
            Text(
              label,
              style: TextStyle(
                fontSize: size * 0.15,
                fontWeight: FontWeight.w600,
                color: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _navigateToRole(String role) {
    Navigator.pushReplacementNamed(context, '/$role');
  }
}
