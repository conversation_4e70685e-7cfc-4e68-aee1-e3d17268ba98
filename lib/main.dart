import 'package:flutter/material.dart';
import 'screens/splash_screen.dart';
import 'screens/student_screen.dart';
import 'screens/teacher_screen.dart';
import 'screens/guest_screen.dart';
import 'screens/auth/login_screen.dart';
import 'screens/auth/signup_screen.dart';
import 'theme/app_theme.dart';

void main() {
  runApp(const MySchoolApp());
}

class MySchoolApp extends StatelessWidget {
  const MySchoolApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'My School',
      debugShowCheckedModeBanner: false,
      theme: AppTheme.lightTheme,
      initialRoute: '/',
      onGenerateRoute: (settings) {
        switch (settings.name) {
          case '/':
            return MaterialPageRoute(
              builder: (context) => const SplashScreen(),
            );
          case '/login':
            final userRole = settings.arguments as String;
            return MaterialPageRoute(
              builder: (context) => LoginScreen(userRole: userRole),
            );
          case '/signup':
            final userRole = settings.arguments as String?;
            return MaterialPageRoute(
              builder: (context) => SignUpScreen(userRole: userRole),
            );
          case '/student':
            return MaterialPageRoute(
              builder: (context) => const StudentScreen(),
            );
          case '/teacher':
            return MaterialPageRoute(
              builder: (context) => const TeacherScreen(),
            );
          case '/guest':
            return MaterialPageRoute(builder: (context) => const GuestScreen());
          default:
            return MaterialPageRoute(
              builder: (context) => const SplashScreen(),
            );
        }
      },
    );
  }
}
