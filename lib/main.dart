import 'package:flutter/material.dart';
import 'screens/splash_screen.dart';
import 'screens/student_screen.dart';
import 'screens/teacher_screen.dart';
import 'screens/guest_screen.dart';
import 'theme/app_theme.dart';

void main() {
  runApp(const MySchoolApp());
}

class MySchoolApp extends StatelessWidget {
  const MySchoolApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'My School',
      debugShowCheckedModeBanner: false,
      theme: AppTheme.lightTheme,
      initialRoute: '/',
      routes: {
        '/': (context) => const SplashScreen(),
        '/student': (context) => const StudentScreen(),
        '/teacher': (context) => const TeacherScreen(),
        '/guest': (context) => const GuestScreen(),
      },
    );
  }
}
